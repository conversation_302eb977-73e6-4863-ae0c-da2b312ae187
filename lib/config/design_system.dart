import 'dart:math';
import 'package:flutter/material.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Design System constants for the Dasso Reader app
///
/// This class contains standardized values for colors, spacing, elevation,
/// typography and other design-related constants to maintain
/// consistency across the app and ensure accessibility compliance.
class DesignSystem {
  // Private constructor to prevent instantiation
  DesignSystem._();

  // =====================================================
  // COLOR SYSTEM - WCAG AA COMPLIANT
  // =====================================================

  /// Primary color palette - Main brand colors
  static const Color primaryColor = Color(0xFF2196F3); // Blue
  static const Color primaryColorLight = Color(0xFF64B5F6);
  static const Color primaryColorDark = Color(0xFF1976D2);
  static const Color primaryColorVariant = Color(0xFF1565C0);

  /// Secondary color palette - Accent colors
  static const Color secondaryColor = Color(0xFF03DAC6); // Teal
  static const Color secondaryColorLight = Color(0xFF4FE6D7);
  static const Color secondaryColorDark = Color(0xFF00A693);
  static const Color secondaryColorVariant = Color(0xFF018786);

  /// Surface colors - Backgrounds and containers
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color surfaceColorLight = Color(0xFFFAFAFA);
  static const Color surfaceColorDark = Color(0xFF121212);
  static const Color surfaceColorVariant = Color(0xFFF5F5F5);

  /// Text colors - WCAG AAA compliant contrast ratios (7:1)
  static const Color textColorPrimary = Color(0xFF1A1A1A); // 7.1:1 contrast
  static const Color textColorSecondary = Color(0xFF4A4A4A); // 7.0:1 contrast
  static const Color textColorHint =
      Color(0xFF757575); // 4.5:1 contrast for large text (WCAG AA)
  static const Color textColorDisabled = Color(0xFFBDBDBD);

  /// Interactive element colors
  static const Color interactiveColor = primaryColor;
  static const Color interactiveColorHover = primaryColorLight;
  static const Color interactiveColorPressed = primaryColorDark;
  static const Color interactiveColorDisabled = Color(0xFFE0E0E0);

  /// State colors - Success, Warning, Error
  static const Color successColor = Color(0xFF4CAF50); // Green
  static const Color successColorLight = Color(0xFF81C784);
  static const Color successColorDark = Color(0xFF388E3C);

  static const Color warningColor = Color(0xFFFF9800); // Orange
  static const Color warningColorLight = Color(0xFFFFB74D);
  static const Color warningColorDark = Color(0xFFF57C00);

  static const Color errorColor = Color(0xFFF44336); // Red
  static const Color errorColorLight = Color(0xFFE57373);
  static const Color errorColorDark = Color(0xFFD32F2F);

  /// Focus and selection colors
  static const Color focusColor = Color(0xFF2196F3);
  static const Color selectionColor = Color(0x332196F3); // 20% opacity
  static const Color highlightColor = Color(0x1A2196F3); // 10% opacity

  /// Dark theme colors - WCAG AAA compliant (7:1)
  static const Color darkSurfaceColor = Color(0xFF121212);
  static const Color darkSurfaceColorVariant = Color(0xFF1E1E1E);
  static const Color darkTextColorPrimary = Color(0xFFF0F0F0); // 7.1:1 contrast
  static const Color darkTextColorSecondary =
      Color(0xFFD0D0D0); // 7.0:1 contrast
  static const Color darkTextColorHint =
      Color(0xFFB0B0B0); // 4.5:1 contrast for large text

  /// Reading theme specific colors
  static const Color readingBackgroundSepia = Color(0xFFFBFBF3);
  static const Color readingBackgroundNight = Color(0xFF1A1A1A);
  static const Color readingBackgroundPaper = Color(0xFFFFFFFF);
  static const Color readingTextSepia = Color(0xFF343434);
  static const Color readingTextNight = Color(0xFFE0E0E0);
  static const Color readingTextPaper = Color(0xFF212121);

  /// Helper method to get theme-appropriate colors (WCAG AAA compliant)
  static Color getTextColor(BuildContext context, {bool isPrimary = true}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (isDark) {
      return isPrimary ? darkTextColorPrimary : darkTextColorSecondary;
    }
    return isPrimary ? textColorPrimary : textColorSecondary;
  }

  /// Get WCAG AAA compliant text color for settings and UI components
  static Color getSettingsTextColor(
    BuildContext context, {
    bool isPrimary = true,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    if (isPrimary) {
      return colorScheme.onSurface;
    } else {
      return colorScheme.onSurfaceVariant;
    }
  }

  /// Helper method to get surface color based on theme
  static Color getSurfaceColor(BuildContext context, {bool isVariant = false}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    if (isDark) {
      return isVariant ? darkSurfaceColorVariant : darkSurfaceColor;
    }
    return isVariant ? surfaceColorVariant : surfaceColor;
  }

  /// Helper method to get interactive color with proper contrast
  static Color getInteractiveColor(
    BuildContext context, {
    bool isDisabled = false,
  }) {
    if (isDisabled) return interactiveColorDisabled;
    return interactiveColor;
  }

  /// Helper method to validate color contrast ratio
  static bool hasValidContrast(
    Color foreground,
    Color background, {
    double minRatio = 7.0,
  }) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    final ratio = (luminance1 > luminance2)
        ? (luminance1 + 0.05) / (luminance2 + 0.05)
        : (luminance2 + 0.05) / (luminance1 + 0.05);
    return ratio >= minRatio;
  }

  /// Helper method to validate WCAG AAA contrast (7:1)
  static bool hasValidContrastAAA(Color foreground, Color background) {
    return hasValidContrast(foreground, background, minRatio: 7.0);
  }

  /// Helper method to validate WCAG AA contrast (4.5:1)
  static bool hasValidContrastAA(Color foreground, Color background) {
    return hasValidContrast(foreground, background, minRatio: 4.5);
  }

  // SPACING SYSTEM
  /// Extra small spacing (4.0)
  static const double spaceXS = 4.0;

  /// Small spacing (8.0)
  static const double spaceS = 8.0;

  /// Medium spacing (16.0) - The base unit
  static const double spaceM = 16.0;

  /// Large spacing (24.0)
  static const double spaceL = 24.0;

  /// Extra large spacing (32.0)
  static const double spaceXL = 32.0;

  /// Double extra large spacing (48.0)
  static const double spaceXXL = 48.0;

  // INSETS
  /// Consistent page padding
  static const EdgeInsets pagePadding = EdgeInsets.all(spaceM);

  /// Small container padding
  static const EdgeInsets containerPaddingSmall = EdgeInsets.all(spaceS);

  /// Medium container padding
  static const EdgeInsets containerPaddingMedium = EdgeInsets.all(spaceM);

  /// Horizontal padding for buttons, text fields, etc.
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  // ELEVATION
  /// No elevation (0.0)
  static const double elevationNone = 0.0;

  /// Extra small elevation (1.0) - subtle
  static const double elevationXS = 1.0;

  /// Small elevation (2.0) - cards, buttons
  static const double elevationS = 2.0;

  /// Medium elevation (4.0) - dialogs, floating action buttons
  static const double elevationM = 4.0;

  /// Large elevation (8.0) - navigation drawers
  static const double elevationL = 8.0;

  /// Extra large elevation (12.0) - modals
  static const double elevationXL = 12.0;

  // BORDER RADIUS
  /// Small border radius (4.0)
  static const double radiusS = 4.0;

  /// Medium border radius (8.0)
  static const double radiusM = 8.0;

  /// Medium-plus border radius (10.0) - slightly larger than medium
  static const double radiusMPlus = 10.0;

  /// Medium-large border radius (12.0) - between medium-plus and large
  static const double radiusML = 12.0;

  /// Large border radius (16.0)
  static const double radiusL = 16.0;

  /// Circle border radius (28.0) - used for circular buttons
  static const double radiusCircle = 28.0;

  // ANIMATION DURATIONS
  /// Fast animations (150ms)
  static const Duration durationFast = Duration(milliseconds: 150);

  /// Medium animations (300ms)
  static const Duration durationMedium = Duration(milliseconds: 300);

  /// Slow animations (500ms)
  static const Duration durationSlow = Duration(milliseconds: 500);

  /// Extra slow animations (600ms) - for complex transitions
  static const Duration durationExtraSlow = Duration(milliseconds: 600);

  // RESPONSIVE BREAKPOINTS
  /// Small phone breakpoint (< 360)
  static const double breakpointSmallPhone = 360;

  /// Mobile breakpoint (< 600)
  static const double breakpointMobile = 600;

  /// Large phone/small tablet breakpoint (< 768)
  static const double breakpointLargePhone = 768;

  /// Tablet breakpoint (< 1024)
  static const double breakpointTablet = 1024;

  /// Desktop breakpoint (≥ 1024)
  static const double breakpointDesktop = 1024;

  /// Large desktop breakpoint (≥ 1440)
  static const double breakpointLargeDesktop = 1440;

  /// Helper to determine if the current width is small phone
  static bool isSmallPhone(BuildContext context) =>
      MediaQuery.of(context).size.width < breakpointSmallPhone;

  /// Helper to determine if the current width is mobile
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < breakpointMobile;

  /// Helper to determine if the current width is large phone
  static bool isLargePhone(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= breakpointMobile && width < breakpointLargePhone;
  }

  /// Helper to determine if the current device is tablet
  static bool isTablet(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final width = mediaQuery.size.width;
    final height = mediaQuery.size.height;
    final devicePixelRatio = mediaQuery.devicePixelRatio;

    // First check: Device model contains "tablet"
    if (_model?.contains('tablet') == true) {
      return true;
    }

    // Second check: Screen size analysis
    // Calculate the diagonal size in inches (approximate)
    final shortSide = width < height ? width : height;
    final longSide = width > height ? width : height;

    // For tablets, we expect:
    // - Larger physical screen size (diagonal > 7 inches typically)
    // - Different aspect ratios than phones
    final diagonalDp = sqrt(pow(longSide, 2) + pow(shortSide, 2));
    final diagonalInches = diagonalDp / (160 * devicePixelRatio);

    // If diagonal is > 7 inches, likely a tablet
    if (diagonalInches > 7.0) {
      return true;
    }

    // Third check: Fallback to width-based detection with adjusted thresholds
    // Use higher threshold for devices with high pixel density
    final adjustedBreakpoint = devicePixelRatio > 2.0
        ? breakpointDesktop * 1.5 // 1536px for high-DPI devices
        : breakpointDesktop; // 1024px for standard devices

    return width >= breakpointLargePhone && width < adjustedBreakpoint;
  }

  /// Helper to determine if the current width is desktop
  static bool isDesktop(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= breakpointDesktop && width < breakpointLargeDesktop;
  }

  /// Helper to determine if the current width is large desktop
  static bool isLargeDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= breakpointLargeDesktop;

  /// Get device size category as enum
  static DeviceSize getDeviceSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < breakpointSmallPhone) return DeviceSize.smallPhone;
    if (width < breakpointMobile) return DeviceSize.mobile;
    if (width < breakpointLargePhone) return DeviceSize.largePhone;
    if (width < breakpointDesktop) return DeviceSize.tablet;
    if (width < breakpointLargeDesktop) return DeviceSize.desktop;
    return DeviceSize.largeDesktop;
  }

  /// Returns adaptive padding based on screen width
  static EdgeInsets getAdaptivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return const EdgeInsets.all(spaceXL);
    } else if (width >= breakpointTablet) {
      return const EdgeInsets.all(spaceL);
    } else {
      return const EdgeInsets.all(spaceM);
    }
  }

  /// Returns adaptive item count for grids based on screen width
  static int getAdaptiveGridCount(
    BuildContext context, {
    double minItemWidth = 120,
  }) {
    final width = MediaQuery.of(context).size.width;
    int count = (width / minItemWidth).floor();
    return count.clamp(2, 8); // Min 2, max 8 columns
  }

  // =====================================================
  // SETTINGS PAGE EXTENSIONS
  // =====================================================

  /// Settings page specific spacing
  static const EdgeInsets settingsItemPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  /// Settings section padding
  static const EdgeInsets settingsSectionPadding = EdgeInsets.all(spaceM);

  /// Settings card margin
  static const EdgeInsets settingsCardMargin = EdgeInsets.only(
    bottom: spaceM,
  );

  /// Settings list tile content padding
  static const EdgeInsets settingsListTilePadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceXS,
  );

  // =====================================================
  // READING EXPERIENCE EXTENSIONS
  // =====================================================

  /// Reading controls padding
  static const EdgeInsets readingControlsPadding = EdgeInsets.all(spaceM);

  /// Reading toolbar height
  static const double readingToolbarHeight = 56.0;

  /// Reading progress indicator height
  static const double readingProgressHeight = 4.0;

  /// Reading menu item padding
  static const EdgeInsets readingMenuItemPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  /// Reading overlay background opacity
  static const double readingOverlayOpacity = 0.8;

  // =====================================================
  // MATERIAL DESIGN 3 STATE LAYER SYSTEM
  // =====================================================

  /// Material Design 3 state layer opacity constants
  /// These values follow the official Material Design 3 specification
  /// for consistent interaction feedback across all components.

  /// Hover state opacity (8%) - for desktop/web hover interactions
  static const double stateLayerHoverOpacity = 0.08;

  /// Focus state opacity (12%) - for keyboard navigation focus
  static const double stateLayerFocusOpacity = 0.12;

  /// Pressed state opacity (12%) - for touch/click interactions
  static const double stateLayerPressedOpacity = 0.12;

  /// Selected state opacity (12%) - for selected items in lists/grids
  static const double stateLayerSelectedOpacity = 0.12;

  /// Dragged state opacity (16%) - for drag and drop interactions
  static const double stateLayerDraggedOpacity = 0.16;

  /// Disabled state opacity (38%) - for disabled interactive elements
  static const double stateLayerDisabledOpacity = 0.38;

  /// Helper method to get state layer color with proper opacity
  ///
  /// [baseColor] - The base color to apply state layer to
  /// [opacity] - The state layer opacity (use constants above)
  /// Returns a color with the specified opacity applied
  static Color getStateLayerColor(Color baseColor, double opacity) {
    return baseColor.withValues(alpha: opacity);
  }

  /// Get hover state layer color
  static Color getHoverStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerHoverOpacity);
  }

  /// Get focus state layer color
  static Color getFocusStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerFocusOpacity);
  }

  /// Get pressed state layer color
  static Color getPressedStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerPressedOpacity);
  }

  /// Get selected state layer color
  static Color getSelectedStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerSelectedOpacity);
  }

  /// Get dragged state layer color
  static Color getDraggedStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerDraggedOpacity);
  }

  /// Get disabled state layer color
  static Color getDisabledStateColor(Color baseColor) {
    return getStateLayerColor(baseColor, stateLayerDisabledOpacity);
  }

  /// Create Material Design 3 compliant WidgetStateProperty for overlay colors
  ///
  /// [baseColor] - The base color for state layers
  /// [includeHover] - Whether to include hover state (default: true)
  /// [includeFocus] - Whether to include focus state (default: true)
  /// [includePressed] - Whether to include pressed state (default: true)
  /// [includeSelected] - Whether to include selected state (default: false)
  /// [includeDragged] - Whether to include dragged state (default: false)
  static WidgetStateProperty<Color?> createStateLayerProperty(
    Color baseColor, {
    bool includeHover = true,
    bool includeFocus = true,
    bool includePressed = true,
    bool includeSelected = false,
    bool includeDragged = false,
  }) {
    return WidgetStateProperty.resolveWith<Color?>((states) {
      if (includeDragged && states.contains(WidgetState.dragged)) {
        return getDraggedStateColor(baseColor);
      }
      if (includePressed && states.contains(WidgetState.pressed)) {
        return getPressedStateColor(baseColor);
      }
      if (includeSelected && states.contains(WidgetState.selected)) {
        return getSelectedStateColor(baseColor);
      }
      if (includeFocus && states.contains(WidgetState.focused)) {
        return getFocusStateColor(baseColor);
      }
      if (includeHover && states.contains(WidgetState.hovered)) {
        return getHoverStateColor(baseColor);
      }
      return null; // No state layer for normal state
    });
  }

  // =====================================================
  // WIDGET COMPONENT EXTENSIONS
  // =====================================================

  /// Standard card padding for widgets
  static const EdgeInsets widgetCardPadding = EdgeInsets.all(spaceM);

  /// Widget item spacing in lists
  static const double widgetItemSpacing = spaceS;

  /// Widget section divider height
  static const double widgetDividerHeight = 1.0;

  /// Widget icon size standards (updated for TabBar navigation requirements)
  static const double widgetIconSizeSmall = 16.0;
  static const double widgetIconSizeMedium =
      28.0; // Increased from 24.0 to 28.0 for TabBar navigation
  static const double widgetIconSizeLarge = 32.0;

  // =====================================================
  // TYPOGRAPHY SYSTEM - FONT SIZE CONSTANTS
  // =====================================================

  /// Font size system following Material Design 3 typography scale
  /// Optimized for Chinese language learning app with proper scaling
  /// and manufacturer adaptation compatibility

  // STANDARD FONT SIZES (Material Design 3 aligned)
  /// Extra small font size (10.0) - Captions, fine print
  static const double fontSizeXS = 10.0;

  /// Small font size (12.0) - Body small, labels, secondary text
  static const double fontSizeS = 12.0;

  /// Medium font size (14.0) - Body medium, default text
  static const double fontSizeM = 14.0;

  /// Large font size (16.0) - Body large, primary text
  static const double fontSizeL = 16.0;

  /// Extra large font size (18.0) - Title medium, headings
  static const double fontSizeXL = 18.0;

  /// Double extra large font size (20.0) - Title large
  static const double fontSizeXXL = 20.0;

  // DISPLAY & HEADING FONT SIZES
  /// Small heading font size (22.0) - Headline small
  static const double fontSizeHeadingS = 22.0;

  /// Medium heading font size (24.0) - Headline medium
  static const double fontSizeHeadingM = 24.0;

  /// Large heading font size (28.0) - Headline large
  static const double fontSizeHeadingL = 28.0;

  /// Extra large heading font size (32.0) - Display small
  static const double fontSizeHeadingXL = 32.0;

  /// Display medium font size (36.0) - Display medium
  static const double fontSizeDisplayM = 36.0;

  /// Display large font size (45.0) - Display large
  static const double fontSizeDisplayL = 45.0;

  /// Display extra large font size (57.0) - Display extra large
  static const double fontSizeDisplayXL = 57.0;

  // CHINESE LANGUAGE SPECIFIC FONT SIZES
  /// Chinese character medium size (20.0) - HSK answer buttons, optimized for multi-character display
  static const double fontSizeChineseM = 20.0;

  /// Chinese character large size (64.0) - Learning cards
  static const double fontSizeChineseL = 64.0;

  /// Chinese character extra large size (72.0) - Hero display
  static const double fontSizeChineseXL = 72.0;

  // PINYIN & PRONUNCIATION FONT SIZES
  /// Pinyin small size (14.0) - Compact pronunciation guides
  static const double fontSizePinyinS = 14.0;

  /// Pinyin medium size (18.0) - Secondary pronunciation
  static const double fontSizePinyinM = 18.0;

  /// Pinyin large size (26.0) - Main pronunciation display
  static const double fontSizePinyinL = 26.0;

  // =====================================================
  // MANUFACTURER-AWARE ADAPTIVE METHODS
  // =====================================================

  // Internal manufacturer detection state
  static AndroidDeviceInfo? _androidInfo;
  static String? _manufacturer;
  static String? _model;
  static bool _isManufacturerDetectionInitialized = false;

  /// Initialize manufacturer detection for pixel-perfect consistency
  /// Call this during app startup for optimal performance
  static Future<void> initializeManufacturerDetection() async {
    if (_isManufacturerDetectionInitialized) return;

    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final deviceInfo = DeviceInfoPlugin();
        _androidInfo = await deviceInfo.androidInfo;
        _manufacturer = _androidInfo?.manufacturer.toLowerCase().trim();
        _model = _androidInfo?.model.toLowerCase().trim();

        if (kDebugMode) {
          AnxLog.info('🎯 DesignSystem: Manufacturer detection initialized');
          AnxLog.info('  📱 Manufacturer: $_manufacturer');
          AnxLog.info('  📱 Model: $_model');
        }
      }
      _isManufacturerDetectionInitialized = true;
    } catch (e) {
      if (kDebugMode) {
        AnxLog.severe(
          '❌ DesignSystem: Failed to initialize manufacturer detection: $e',
        );
      }
      // Fallback to default values
      _manufacturer = 'unknown';
      _model = 'unknown';
      _isManufacturerDetectionInitialized = true;
    }
  }

  /// Get manufacturer-adjusted spacing using design system constants
  /// Ensures pixel-perfect TabBar consistency across all Android devices
  static double getAdaptiveSpacingWithManufacturerAdjustment(
    BuildContext context,
    double baseSpacing,
  ) {
    final multiplier = _getManufacturerSpacingMultiplier();
    return baseSpacing * multiplier;
  }

  /// Get manufacturer-adjusted padding that respects design system patterns
  static EdgeInsets getAdaptivePaddingWithManufacturerAdjustment(
    BuildContext context,
    EdgeInsets basePadding,
  ) {
    final multiplier = _getManufacturerSpacingMultiplier();
    return EdgeInsets.fromLTRB(
      basePadding.left * multiplier,
      basePadding.top * multiplier,
      basePadding.right * multiplier,
      basePadding.bottom * multiplier,
    );
  }

  /// Get manufacturer-adjusted TabBar spacing using design system constants
  /// Professional method for pixel-perfect TabBar consistency
  static double getTabBarSpacing(BuildContext context) {
    return getAdaptiveSpacingWithManufacturerAdjustment(context, spaceS);
  }

  /// Get manufacturer-adjusted TabBar spacing with text width compensation
  /// This method accounts for text width changes caused by font weight adjustments
  /// Ensures pixel-perfect spacing consistency across all manufacturers
  static double getCompensatedTabBarSpacing(BuildContext context) {
    final baseSpacing =
        getAdaptiveSpacingWithManufacturerAdjustment(context, spaceS);
    final textWidthCompensation = getTextWidthCompensationMultiplier();

    // Apply text width compensation to maintain visual spacing consistency
    return baseSpacing * textWidthCompensation;
  }

  /// Get manufacturer-adjusted TabBar padding using design system patterns
  static EdgeInsets getTabBarPadding(BuildContext context) {
    return getAdaptivePaddingWithManufacturerAdjustment(
      context,
      const EdgeInsets.symmetric(horizontal: spaceS, vertical: spaceXS),
    );
  }

  /// Get manufacturer-adjusted TabBar padding with text width compensation
  /// Ensures consistent visual padding despite font weight-induced text width changes
  static EdgeInsets getCompensatedTabBarPadding(BuildContext context) {
    final basePadding = getAdaptivePaddingWithManufacturerAdjustment(
      context,
      const EdgeInsets.symmetric(horizontal: spaceS, vertical: spaceXS),
    );
    final textWidthCompensation = getTextWidthCompensationMultiplier();

    // Apply text width compensation to horizontal padding only
    return EdgeInsets.symmetric(
      horizontal: basePadding.horizontal * textWidthCompensation,
      vertical: basePadding.vertical,
    );
  }

  /// Get manufacturer-adjusted font weight for pixel-perfect text rendering
  /// Converts base FontWeight to manufacturer-specific weight for consistency
  static FontWeight getAdjustedFontWeight(
    FontWeight baseFontWeight, {
    String? text,
  }) {
    final multiplier = getFontWeightMultiplier(text: text);

    // Convert FontWeight to numeric value for calculation
    int baseWeight = _fontWeightToNumeric(baseFontWeight);

    // Apply manufacturer adjustment
    int adjustedWeight = (baseWeight * multiplier).round().clamp(100, 900);

    // Convert back to FontWeight
    return _numericToFontWeight(adjustedWeight);
  }

  /// Get manufacturer-adjusted icon size for perfect visual balance
  static double getAdjustedIconSize(double baseIconSize) {
    return baseIconSize * getIconSizeMultiplier();
  }

  /// Get manufacturer-adjusted elevation for consistent shadows
  static double getAdjustedElevation(double baseElevation) {
    return baseElevation * getElevationMultiplier();
  }

  /// Helper method to convert FontWeight to numeric value
  static int _fontWeightToNumeric(FontWeight fontWeight) {
    switch (fontWeight) {
      case FontWeight.w100:
        return 100;
      case FontWeight.w200:
        return 200;
      case FontWeight.w300:
        return 300;
      case FontWeight.w400:
        return 400;
      case FontWeight.w500:
        return 500;
      case FontWeight.w600:
        return 600;
      case FontWeight.w700:
        return 700;
      case FontWeight.w800:
        return 800;
      case FontWeight.w900:
        return 900;
      default:
        return 400; // Normal weight fallback
    }
  }

  /// Helper method to convert numeric value back to FontWeight
  static FontWeight _numericToFontWeight(int weight) {
    if (weight <= 150) return FontWeight.w100;
    if (weight <= 250) return FontWeight.w200;
    if (weight <= 350) return FontWeight.w300;
    if (weight <= 450) return FontWeight.w400;
    if (weight <= 550) return FontWeight.w500;
    if (weight <= 650) return FontWeight.w600;
    if (weight <= 750) return FontWeight.w700;
    if (weight <= 850) return FontWeight.w800;
    return FontWeight.w900;
  }

  /// Private helper to get manufacturer-specific spacing multiplier
  /// Integrated into design system for pixel-perfect consistency
  static double _getManufacturerSpacingMultiplier() {
    if (!_isManufacturerDetectionInitialized) {
      return 1.0; // Default multiplier
    }

    switch (_manufacturer) {
      // Huawei/Honor devices (EMUI/Magic UI) - tighter spacing for consistency
      case 'huawei':
      case 'honor':
        return _getHuaweiSpacingMultiplier();

      // Samsung devices (One UI)
      case 'samsung':
        return 1.02;

      // Xiaomi devices (MIUI)
      case 'xiaomi':
      case 'redmi':
      case 'poco':
        return 0.98;

      // OnePlus/Oppo devices
      case 'oneplus':
      case 'oppo':
        return 0.99;

      // Google Pixel devices (reference standard)
      case 'google':
        return 1.0;

      // Other manufacturers
      default:
        return 1.0;
    }
  }

  /// Huawei-specific spacing adjustments for pixel-perfect consistency
  static double _getHuaweiSpacingMultiplier() {
    // Significantly tighter spacing for Huawei devices to match Pixel 9 Pro
    if (_model?.contains('p60') == true || _model?.contains('mate') == true) {
      return 0.75; // Premium models
    } else if (_model?.contains('nova') == true) {
      return 0.78; // Mid-range models
    }
    return 0.76; // Default Huawei adjustment
  }

  /// Get manufacturer-specific font weight multiplier for consistent text rendering
  /// Ensures text appears identical to Pixel 9 Pro across all manufacturers
  /// Enhanced with character-specific adjustments for Latin vs Chinese text
  static double getFontWeightMultiplier({String? text}) {
    if (!_isManufacturerDetectionInitialized) {
      return 1.0; // Default multiplier
    }

    // Check if text contains primarily Latin characters (like "HSK", "Notes")
    final isLatinTextResult = text != null && isLatinText(text);

    switch (_manufacturer) {
      // Huawei/Honor devices (EMUI/Magic UI) - fonts render lighter
      case 'huawei':
      case 'honor':
        // Special handling for UI icons and Latin text
        if (isLatinTextResult && text.length <= 2) {
          // For single character UI icons (like "A"), use minimal adjustment
          return 1.02; // Prevents FontWeight.w500 from jumping to w600
        }
        // Latin characters need less weight adjustment on Huawei EMUI
        return isLatinTextResult
            ? 1.05
            : 1.15; // Reduced for Latin, normal for Chinese

      // Samsung devices (One UI) - fonts render slightly heavier
      case 'samsung':
        return 0.95; // Decrease font weight for consistency

      // Xiaomi devices (MIUI) - slight font rendering differences
      case 'xiaomi':
      case 'redmi':
      case 'poco':
        return 1.02; // Minor adjustment for MIUI

      // OnePlus/Oppo devices - generally consistent
      case 'oneplus':
      case 'oppo':
        return 0.98; // Slight adjustment for OxygenOS/ColorOS

      // Vivo devices (Funtouch OS)
      case 'vivo':
        return 1.03; // Funtouch OS font rendering compensation

      // Realme devices (Realme UI)
      case 'realme':
        return 1.01; // Minor Realme UI adjustment

      // Google Pixel devices (reference standard)
      case 'google':
        return 1.0; // Perfect reference

      // Other manufacturers
      default:
        return 1.0; // Conservative default
    }
  }

  /// Helper method to detect if text is primarily Latin characters
  static bool isLatinText(String text) {
    if (text.isEmpty) return false;

    // Count Latin characters (A-Z, a-z, 0-9, common punctuation)
    final latinChars = text.runes.where((rune) {
      return (rune >= 0x0020 && rune <= 0x007F) || // Basic Latin
          (rune >= 0x00A0 && rune <= 0x00FF); // Latin-1 Supplement
    }).length;

    // Consider text as Latin if more than 70% of characters are Latin
    return latinChars / text.length > 0.7;
  }

  /// Get manufacturer-specific text width compensation multiplier
  /// Compensates for text width changes caused by font weight adjustments
  /// This ensures TabBar spacing remains consistent despite font weight changes
  static double getTextWidthCompensationMultiplier() {
    if (!_isManufacturerDetectionInitialized) {
      return 1.0; // Default multiplier
    }

    switch (_manufacturer) {
      // Huawei/Honor devices - compensate for wider text due to increased font weight
      case 'huawei':
      case 'honor':
        return 0.94; // Compensate for 1.15x font weight making text ~6% wider

      // Samsung devices - compensate for narrower text due to decreased font weight
      case 'samsung':
        return 1.03; // Compensate for 0.95x font weight making text slightly narrower

      // Xiaomi devices - minor compensation for MIUI font rendering
      case 'xiaomi':
      case 'redmi':
      case 'poco':
        return 0.99; // Minor compensation for 1.02x font weight

      // OnePlus/Oppo devices - minor compensation
      case 'oneplus':
      case 'oppo':
        return 1.01; // Minor compensation for 0.98x font weight

      // Vivo devices - compensation for font rendering
      case 'vivo':
        return 0.98; // Compensate for 1.03x font weight

      // Realme devices - minimal compensation
      case 'realme':
        return 0.995; // Minimal compensation for 1.01x font weight

      // Google Pixel devices (reference standard)
      case 'google':
        return 1.0; // Perfect reference - no compensation needed

      // Other manufacturers
      default:
        return 1.0; // Conservative default
    }
  }

  /// Get manufacturer-specific icon size multiplier for perfect visual balance
  /// Ensures icons appear identical to Pixel 9 Pro across all manufacturers
  static double getIconSizeMultiplier() {
    if (!_isManufacturerDetectionInitialized) {
      return 1.0; // Default multiplier
    }

    switch (_manufacturer) {
      // Huawei/Honor devices (EMUI/Magic UI) - icon scaling adjustment
      case 'huawei':
      case 'honor':
        return 0.98; // Slightly smaller for visual balance

      // Samsung devices (One UI) - icon enhancement compensation
      case 'samsung':
        return 0.99; // Minor size adjustment

      // Xiaomi devices (MIUI) - icon rendering adjustment
      case 'xiaomi':
      case 'redmi':
      case 'poco':
        return 1.01; // MIUI icon scaling

      // OnePlus/Oppo devices - standard scaling
      case 'oneplus':
      case 'oppo':
        return 1.00; // OxygenOS/ColorOS standard

      // Vivo devices (Funtouch OS)
      case 'vivo':
        return 1.01; // Funtouch OS icon adjustment

      // Realme devices (Realme UI)
      case 'realme':
        return 1.00; // Realme UI standard

      // Google Pixel devices (reference standard)
      case 'google':
        return 1.0; // Perfect reference

      // Other manufacturers
      default:
        return 1.0; // Conservative default
    }
  }

  /// Get manufacturer-specific elevation multiplier for consistent shadows
  /// Ensures shadow depth appears identical to Pixel 9 Pro across all manufacturers
  static double getElevationMultiplier() {
    if (!_isManufacturerDetectionInitialized) {
      return 1.0; // Default multiplier
    }

    switch (_manufacturer) {
      // Huawei/Honor devices (EMUI/Magic UI) - shadow enhancement
      case 'huawei':
      case 'honor':
        return 1.05; // EMUI shadow rendering enhancement

      // Samsung devices (One UI) - shadow optimization
      case 'samsung':
        return 0.97; // One UI shadow adjustment

      // Xiaomi devices (MIUI) - shadow adjustment
      case 'xiaomi':
      case 'redmi':
      case 'poco':
        return 1.02; // MIUI shadow compensation

      // OnePlus/Oppo devices - shadow refinement
      case 'oneplus':
      case 'oppo':
        return 0.98; // OxygenOS/ColorOS shadow adjustment

      // Vivo devices (Funtouch OS)
      case 'vivo':
        return 1.01; // Funtouch OS shadow adjustment

      // Realme devices (Realme UI)
      case 'realme':
        return 1.00; // Realme UI standard

      // Google Pixel devices (reference standard)
      case 'google':
        return 1.0; // Perfect reference

      // Other manufacturers
      default:
        return 1.0; // Conservative default
    }
  }

  /// Get device information for debugging (integrated into design system)
  static Map<String, dynamic> getManufacturerDebugInfo() {
    return {
      'manufacturer': _manufacturer ?? 'unknown',
      'model': _model ?? 'unknown',
      'isInitialized': _isManufacturerDetectionInitialized,
      'spacingMultiplier': _getManufacturerSpacingMultiplier(),
      'fontWeightMultiplier': getFontWeightMultiplier(),
      'fontWeightMultiplierLatin': getFontWeightMultiplier(text: 'HSK'),
      'fontWeightMultiplierChinese': getFontWeightMultiplier(text: '书架'),
      'textWidthCompensationMultiplier': getTextWidthCompensationMultiplier(),
      'iconSizeMultiplier': getIconSizeMultiplier(),
      'elevationMultiplier': getElevationMultiplier(),
      'isReferenceDevice':
          _manufacturer == 'google' && (_model?.contains('pixel 9') == true),
    };
  }

  /// Check if the current device is the reference Pixel 9 Pro
  static bool isReferenceDevice() {
    return _manufacturer == 'google' &&
        (_model?.contains('pixel 9 pro') == true ||
            _model?.contains('pixel 9') == true);
  }

  /// Get a human-readable device description
  static String getDeviceDescription() {
    if (!_isManufacturerDetectionInitialized) {
      return 'Device detection not initialized';
    }

    final manufacturer = _manufacturer?.toUpperCase() ?? 'UNKNOWN';
    final model = _model?.toUpperCase() ?? 'UNKNOWN';

    return '$manufacturer $model';
  }

  // =====================================================
  // LAYOUT SYSTEM - COMPREHENSIVE SPACING PATTERNS
  // =====================================================

  // COMMON LAYOUT PATTERNS
  /// Standard list item padding
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  /// Compact list item padding for dense layouts
  static const EdgeInsets listItemPaddingCompact = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceXS,
  );

  /// Card content padding
  static const EdgeInsets cardContentPadding = EdgeInsets.all(spaceM);

  /// Card margin between cards
  static const EdgeInsets cardMargin = EdgeInsets.all(spaceS);

  /// Section header padding
  static const EdgeInsets sectionHeaderPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  /// Dialog content padding
  static const EdgeInsets dialogContentPadding = EdgeInsets.all(spaceL);

  /// Bottom sheet content padding
  static const EdgeInsets bottomSheetPadding = EdgeInsets.all(spaceM);

  // BUTTON LAYOUT PATTERNS
  /// Standard button padding
  static const EdgeInsets standardButtonPadding = EdgeInsets.symmetric(
    horizontal: spaceL,
    vertical: spaceS,
  );

  /// Compact button padding
  static const EdgeInsets compactButtonPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceXS,
  );

  /// Icon button padding
  static const EdgeInsets iconButtonPadding = EdgeInsets.all(spaceS);

  // FORM LAYOUT PATTERNS
  /// Form field padding
  static const EdgeInsets formFieldPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  /// Form section spacing
  static const double formSectionSpacing = spaceL;

  /// Form field spacing
  static const double formFieldSpacing = spaceM;

  // SPACING UTILITIES
  /// Tiny spacing for fine adjustments (2.0)
  static const double spaceTiny = 2.0;

  /// Micro spacing for minimal adjustments (1.0)
  static const double spaceMicro = 1.0;

  /// Small-plus spacing (5.0) - between XS and XsS
  static const double spaceXsPlus = 5.0;

  /// Intermediate spacing between XS and S (6.0)
  static const double spaceXsS = 6.0;

  /// Intermediate spacing between S and M (12.0)
  static const double spaceSM = 12.0;

  /// Section divider spacing
  static const double sectionDividerSpacing = spaceL;

  /// Content block spacing
  static const double contentBlockSpacing = spaceXL;

  // COMMON SIZEBOX PATTERNS
  /// Tiny vertical spacing
  static const SizedBox verticalSpaceTiny = SizedBox(height: spaceTiny);

  /// Micro vertical spacing
  static const SizedBox verticalSpaceMicro = SizedBox(height: spaceMicro);

  /// Extra small vertical spacing
  static const SizedBox verticalSpaceXS = SizedBox(height: spaceXS);

  /// Small vertical spacing
  static const SizedBox verticalSpaceS = SizedBox(height: spaceS);

  /// Medium vertical spacing
  static const SizedBox verticalSpaceM = SizedBox(height: spaceM);

  /// Large vertical spacing
  static const SizedBox verticalSpaceL = SizedBox(height: spaceL);

  /// Extra large vertical spacing
  static const SizedBox verticalSpaceXL = SizedBox(height: spaceXL);

  /// Tiny horizontal spacing
  static const SizedBox horizontalSpaceTiny = SizedBox(width: spaceTiny);

  /// Micro horizontal spacing
  static const SizedBox horizontalSpaceMicro = SizedBox(width: spaceMicro);

  /// Extra small horizontal spacing
  static const SizedBox horizontalSpaceXS = SizedBox(width: spaceXS);

  /// Small horizontal spacing
  static const SizedBox horizontalSpaceS = SizedBox(width: spaceS);

  /// Medium horizontal spacing
  static const SizedBox horizontalSpaceM = SizedBox(width: spaceM);

  /// Large horizontal spacing
  static const SizedBox horizontalSpaceL = SizedBox(width: spaceL);

  /// Extra large horizontal spacing
  static const SizedBox horizontalSpaceXL = SizedBox(width: spaceXL);

  /// Widget minimum touch target (accessibility compliance)
  static const double widgetMinTouchTarget = 44.0;

  // =====================================================
  // ADAPTIVE LAYOUT METHODS
  // =====================================================

  /// Returns adaptive content padding based on screen size
  static EdgeInsets getAdaptiveContentPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return const EdgeInsets.all(spaceXL);
    } else if (width >= breakpointTablet) {
      return const EdgeInsets.all(spaceL);
    } else {
      return const EdgeInsets.all(spaceM);
    }
  }

  /// Returns adaptive list item padding based on screen size
  static EdgeInsets getAdaptiveListItemPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return const EdgeInsets.symmetric(
        horizontal: spaceXL,
        vertical: spaceM,
      );
    } else if (width >= breakpointTablet) {
      return const EdgeInsets.symmetric(
        horizontal: spaceL,
        vertical: spaceS,
      );
    } else {
      return listItemPadding;
    }
  }

  /// Returns adaptive card margin based on screen size
  static EdgeInsets getAdaptiveCardMargin(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return const EdgeInsets.all(spaceM);
    } else if (width >= breakpointTablet) {
      return const EdgeInsets.all(spaceS);
    } else {
      return const EdgeInsets.symmetric(
        horizontal: spaceXS,
        vertical: spaceS,
      );
    }
  }

  /// Returns adaptive section spacing based on screen size
  static double getAdaptiveSectionSpacing(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return spaceXXL;
    } else if (width >= breakpointTablet) {
      return spaceXL;
    } else {
      return spaceL;
    }
  }

  /// Returns adaptive button padding based on screen size
  static EdgeInsets getAdaptiveButtonPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return const EdgeInsets.symmetric(
        horizontal: spaceXL,
        vertical: spaceM,
      );
    } else if (width >= breakpointTablet) {
      return const EdgeInsets.symmetric(
        horizontal: spaceL,
        vertical: spaceS,
      );
    } else {
      return standardButtonPadding;
    }
  }

  // =====================================================
  // LAYOUT UTILITIES & HELPERS
  // =====================================================

  /// Creates consistent spacing between form fields
  static Widget createFormFieldSpacing() =>
      const SizedBox(height: formFieldSpacing);

  /// Creates consistent spacing between sections
  static Widget createSectionSpacing() =>
      const SizedBox(height: sectionDividerSpacing);

  /// Creates adaptive spacing based on context
  static Widget createAdaptiveSpacing(BuildContext context) {
    return SizedBox(height: getAdaptiveSectionSpacing(context));
  }

  /// Creates consistent horizontal divider with proper spacing
  static Widget createSectionDivider({Color? color}) {
    return Container(
      height: widgetDividerHeight,
      margin: const EdgeInsets.symmetric(vertical: sectionDividerSpacing),
      color: color,
    );
  }

  /// Ensures minimum touch target size for accessibility
  static BoxConstraints getMinTouchTargetConstraints() {
    return const BoxConstraints(
      minWidth: widgetMinTouchTarget,
      minHeight: widgetMinTouchTarget,
    );
  }

  /// Returns appropriate spacing for different content density levels
  static double getContentDensitySpacing(ContentDensity density) {
    switch (density) {
      case ContentDensity.compact:
        return spaceXS;
      case ContentDensity.comfortable:
        return spaceS;
      case ContentDensity.standard:
        return spaceM;
      case ContentDensity.spacious:
        return spaceL;
    }
  }

  /// Returns appropriate padding for different content density levels
  static EdgeInsets getContentDensityPadding(ContentDensity density) {
    final spacing = getContentDensitySpacing(density);
    return EdgeInsets.all(spacing);
  }
  // =====================================================
  // ADDITIONAL ADAPTIVE HELPER METHODS
  // =====================================================

  /// Returns adaptive font size based on screen width
  static double getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return baseFontSize * 1.1; // Slightly larger on desktop
    } else if (width >= breakpointTablet) {
      return baseFontSize * 1.05; // Slightly larger on tablet
    } else {
      return baseFontSize; // Base size on mobile
    }
  }

  /// Get manufacturer-adjusted font size for pixel-perfect text rendering
  /// Combines responsive design with manufacturer-specific adjustments
  static double getAdjustedFontSize(
    BuildContext context,
    double baseFontSize, {
    String? text,
  }) {
    // First apply responsive scaling
    final responsiveFontSize = getAdaptiveFontSize(context, baseFontSize);

    // Font size adjustment based on manufacturer font rendering characteristics
    // Compensates for visual differences in text rendering
    double fontSizeMultiplier = 1.0;

    if (_isManufacturerDetectionInitialized) {
      switch (_manufacturer) {
        // Huawei/Honor devices - fonts render slightly smaller visually
        case 'huawei':
        case 'honor':
          fontSizeMultiplier = 1.02; // Slightly larger to compensate
          break;

        // Samsung devices - fonts render slightly larger visually
        case 'samsung':
          fontSizeMultiplier = 0.98; // Slightly smaller to compensate
          break;

        // Other manufacturers use standard sizing
        default:
          fontSizeMultiplier = 1.0;
          break;
      }
    }

    return responsiveFontSize * fontSizeMultiplier;
  }

  /// Get font size with accessibility scaling support
  /// Respects user's system text scaling preferences while maintaining readability
  static double getAccessibleFontSize(
    BuildContext context,
    double baseFontSize, {
    double? minFontSize,
    double? maxFontSize,
  }) {
    final textScaler = MediaQuery.textScalerOf(context);
    final scaledFontSize = textScaler.scale(baseFontSize);

    // Apply constraints to prevent text from becoming too small or too large
    final constrainedFontSize = scaledFontSize.clamp(
      minFontSize ?? baseFontSize * 0.8,
      maxFontSize ?? baseFontSize * 1.5,
    );

    return constrainedFontSize;
  }

  // =====================================================
  // CONVENIENCE METHODS FOR COMMON FONT SIZE PATTERNS
  // =====================================================

  /// Get font size for body text with full adaptation
  /// Combines responsive design, manufacturer adjustments, and accessibility
  static double getBodyFontSize(BuildContext context, {String? text}) {
    return getAdjustedFontSize(context, fontSizeM, text: text);
  }

  /// Get font size for headings with full adaptation
  static double getHeadingFontSize(BuildContext context, {String? text}) {
    return getAdjustedFontSize(context, fontSizeHeadingM, text: text);
  }

  /// Get font size for Chinese characters with full adaptation
  static double getChineseFontSize(BuildContext context, {String? text}) {
    return getAdjustedFontSize(context, fontSizeChineseM, text: text);
  }

  /// Get font size for pinyin with full adaptation
  static double getPinyinFontSize(BuildContext context, {String? text}) {
    return getAdjustedFontSize(context, fontSizePinyinM, text: text);
  }

  /// Get font size for labels with accessibility support
  static double getLabelFontSize(BuildContext context, {String? text}) {
    return getAccessibleFontSize(
      context,
      fontSizeS,
      minFontSize: fontSizeXS,
      maxFontSize: fontSizeM,
    );
  }

  /// Returns adaptive icon size based on screen width
  static double getAdaptiveIconSize(BuildContext context, double baseIconSize) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return baseIconSize * 1.2;
    } else if (width >= breakpointTablet) {
      return baseIconSize * 1.1;
    } else {
      return baseIconSize;
    }
  }

  /// Returns adaptive list tile height based on screen width
  static double getAdaptiveListTileHeight(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return 64.0; // Larger touch targets on desktop
    } else if (width >= breakpointTablet) {
      return 56.0; // Medium touch targets on tablet
    } else {
      return 48.0; // Compact but accessible on mobile
    }
  }

  /// Returns adaptive dialog width based on screen width
  static double getAdaptiveDialogWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= breakpointDesktop) {
      return width * 0.4; // 40% of screen width on desktop
    } else if (width >= breakpointTablet) {
      return width * 0.6; // 60% of screen width on tablet
    } else {
      return width * 0.9; // 90% of screen width on mobile
    }
  }

  /// Returns adaptive bottom sheet height based on screen height
  static double getAdaptiveBottomSheetHeight(BuildContext context) {
    final height = MediaQuery.of(context).size.height;

    if (height >= 800) {
      return height * 0.6; // 60% on tall screens
    } else if (height >= 600) {
      return height * 0.7; // 70% on medium screens
    } else {
      return height * 0.8; // 80% on short screens
    }
  }

  // =====================================================
  // TAB LAYOUT RESPONSIVE UTILITIES
  // =====================================================

  /// Get density-aware spacing specifically for navigation tabs
  static double getAdaptiveTabSpacing(BuildContext context) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    const baseSpacing = spaceXS; // 4.0

    // Normalize to standard Android density (3.0)
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.8, 1.3);

    // Adjust for device size category
    if (isSmallPhone(context)) {
      densityFactor *= 0.8; // Tighter spacing on small phones
    } else if (isTablet(context)) {
      densityFactor *= 1.1; // More generous spacing on tablets
    }

    return baseSpacing * densityFactor;
  }

  /// Get density-aware tab height with accessibility compliance
  static double getAdaptiveTabHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final textScaler = mediaQuery.textScaler;

    const baseHeight = 72.0;
    const minHeight = 48.0; // Accessibility minimum touch target

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for user text size preferences
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (isSmallPhone(context)) {
      deviceFactor = 0.9; // Slightly smaller on small phones
    } else if (isTablet(context)) {
      deviceFactor = 1.1; // Slightly larger on tablets
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return calculatedHeight < minHeight ? minHeight : calculatedHeight;
  }

  /// Get adaptive font size for tab labels
  static double getAdaptiveTabFontSize(BuildContext context) {
    final textScaler = MediaQuery.of(context).textScaler;
    const baseFontSize = 12.0;

    // Apply text scaling with constraints
    double scaledSize = textScaler.scale(baseFontSize);

    // Constrain to prevent overflow while maintaining readability
    double constrainedSize = scaledSize.clamp(10.0, 16.0);

    // Slight adjustment for very small screens
    if (isSmallPhone(context)) {
      constrainedSize = constrainedSize.clamp(10.0, 14.0);
    }

    return constrainedSize;
  }

  /// Get adaptive icon size for tab icons
  static double getAdaptiveTabIconSize(BuildContext context) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    const baseIconSize = 28.0; // Increased from 24.0 to 28.0

    // Scale based on device density
    double adaptiveSize =
        baseIconSize * (devicePixelRatio / 3.0).clamp(0.9, 1.2);

    // Prevent icons from becoming too large on high-density screens (proportionally increased)
    return adaptiveSize.clamp(
      24.0,
      32.0,
    ); // Increased from 20.0-28.0 to 24.0-32.0
  }
}

/// Content density levels for adaptive layouts
enum ContentDensity {
  compact,
  comfortable,
  standard,
  spacious,
}

/// Device size categories for responsive design
enum DeviceSize {
  smallPhone,
  mobile,
  largePhone,
  tablet,
  desktop,
  largeDesktop,
}
